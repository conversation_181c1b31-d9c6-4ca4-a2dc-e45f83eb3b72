import { useState, useEffect } from 'react';
import { getSignedUrl } from '../../utils/fileUtils';

interface FileWithSignedUrlProps {
  path: string;
  label?: string;
  showLoading?: boolean;
  className?: string;
}

/**
 * Component to render a file with a signed URL
 * Handles both image and PDF files from the consolidated certificateuploads bucket
 */
export const FileWithSignedUrl = ({
  path,
  label = 'Datei anzeigen',
  showLoading = true,
  className = ''
}: FileWithSignedUrlProps) => {
  const [signedUrl, setSignedUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fileType, setFileType] = useState<'pdf' | 'image' | 'other'>('other');

  useEffect(() => {
    if (path) {
      setIsLoading(true);
      setError(null);

      // Determine file type from path
      if (path.toLowerCase().endsWith('.pdf')) {
        setFileType('pdf');
      } else if (
        path.toLowerCase().endsWith('.jpg') ||
        path.toLowerCase().endsWith('.jpeg') ||
        path.toLowerCase().endsWith('.png') ||
        path.toLowerCase().endsWith('.webp') ||
        path.toLowerCase().endsWith('.gif')
      ) {
        setFileType('image');
      } else {
        setFileType('other');
      }

      // Get signed URL
      getSignedUrl(path).then(url => {
        if (url) {
          setSignedUrl(url);
        } else {
          setError('Datei konnte nicht geladen werden');
        }
        setIsLoading(false);
      }).catch(err => {
        console.error('Error getting signed URL:', err);
        setError('Fehler beim Laden der Datei');
        setIsLoading(false);
      });
    }
  }, [path]);

  if (isLoading && showLoading) {
    return <span className="text-gray-400">Wird geladen...</span>;
  }

  if (error || !signedUrl) {
    return <span className="text-gray-400">{error || 'Datei nicht verfügbar'}</span>;
  }

  // For PDF files, we can add a PDF icon
  const isPdf = fileType === 'pdf' || path.toLowerCase().endsWith('.pdf');
  
  return (
    <div className={`flex items-center ${className}`}>
      {isPdf && (
        <svg className="w-4 h-4 mr-1 text-red-600" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 2a2 2 0 00-2 2v8a2 2 0 002 2h6a2 2 0 002-2V6.414A2 2 0 0016.414 5L14 2.586A2 2 0 0012.586 2H9z" />
          <path d="M3 8a2 2 0 012-2h2a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2V8z" />
        </svg>
      )}
      <a
        href={signedUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="text-green-600 hover:underline"
      >
        {label}
      </a>
    </div>
  );
};