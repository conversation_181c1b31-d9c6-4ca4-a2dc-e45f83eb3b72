import { useState, useEffect } from 'react';
import { useForm, useField } from '@tanstack/react-form';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import { Link, useNavigate } from '@tanstack/react-router';
import { supabase } from '../../lib/supabase';
import { useCertificate } from '../../contexts/CertificateContext';
import { ActiveCertificateIndicator } from '../../components/ui/ActiveCertificateIndicator';
import { RadioField } from '../../components/ui/RadioField';
import { FileWithSignedUrl } from '../../components/ui/FileWithSignedUrl';

// Define the certificate types
type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

// This component is now imported from '../../components/ui/FileWithSignedUrl'
// and the local implementation has been removed to avoid naming conflicts

// Utility functions for date calculations
const calculateDateWithYearOffset = (dateString: string, yearOffset: number): string => {
  if (!dateString) return '';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';

  // Add or subtract the specified number of years
  date.setFullYear(date.getFullYear() + yearOffset);

  // Return in YYYY-MM-DD format for date inputs
  return date.toISOString().split('T')[0];
};

const calculateAllDatesFromStartDate = (startDate: string): {
  jahr1Bis: string;
  jahr2Von: string;
  jahr2Bis: string;
  jahr3Von: string;
  jahr3Bis: string;
} => {
  if (!startDate) {
    return {
      jahr1Bis: '',
      jahr2Von: '',
      jahr2Bis: '',
      jahr3Von: '',
      jahr3Bis: ''
    };
  }

  return {
    jahr1Bis: calculateDateWithYearOffset(startDate, 1), // One year after start date
    jahr2Von: calculateDateWithYearOffset(startDate, -1), // One year before start date
    jahr2Bis: calculateDateWithYearOffset(startDate, 0), // Same as start date (end of Jahr 2)
    jahr3Von: calculateDateWithYearOffset(startDate, -2), // Two years before start date
    jahr3Bis: calculateDateWithYearOffset(startDate, -1) // One year before start date (end of Jahr 3)
  };
};

// Utility function to calculate percentage from date ranges
const calculateLeerstandPercentage = (
  leerstandVon: string,
  leerstandBis: string,
  periodVon: string,
  periodBis: string
): number => {
  if (!leerstandVon || !leerstandBis || !periodVon || !periodBis) return 0;

  const leerstandStart = new Date(leerstandVon);
  const leerstandEnd = new Date(leerstandBis);
  const periodStart = new Date(periodVon);
  const periodEnd = new Date(periodBis);

  // Validate dates
  if (isNaN(leerstandStart.getTime()) || isNaN(leerstandEnd.getTime()) ||
      isNaN(periodStart.getTime()) || isNaN(periodEnd.getTime())) {
    return 0;
  }

  // Calculate durations in milliseconds
  const leerstandDuration = leerstandEnd.getTime() - leerstandStart.getTime();
  const totalDuration = periodEnd.getTime() - periodStart.getTime();

  // Ensure positive durations
  if (leerstandDuration <= 0 || totalDuration <= 0) return 0;

  // Calculate percentage and round to 2 decimal places
  const percentage = (leerstandDuration / totalDuration) * 100;
  return Math.round(percentage * 100) / 100;
};

// Define the form schema using Zod
const verbrauchSchema = z.object({
  // Energieträger 1 (ETr1)
  ETr1_Kategorie: z.string().optional(),
  ETr1_Heizung: z.enum(['0', '1']).default('1'),
  ETr1_TWW: z.enum(['0', '1']).default('1'),
  ETr1_ZusatzHz: z.enum(['0', '1']).default('0'),
  ETr1_Lueften: z.enum(['0', '1']).default('0'),
  ETr1_Licht: z.enum(['0', '1']).default('0'),
  ETr1_Kuehlen: z.enum(['0', '1']).default('0'),
  ETr1_Sonst: z.enum(['0', '1']).default('0'),
  // Primärenergiefaktor is fixed at 1 per business requirements - hidden from user interface
  ETr1_PrimFaktor: z.string().default('1'),
  ETr1_Anteil_erneuerbar: z.string().optional(),
  ETr1_Anteil_KWK: z.string().optional(),
  ETr1_isFw: z.enum(['0', '1']).default('0'),
  ETr1_gebaeudeNahErzeugt: z.enum(['0', '1']).default('0'),
  ETr1_Name: z.string().optional(),

  // Jahr 1 für ETr1
  ETr1_Jahr1_von: z.string().optional(),
  ETr1_Jahr1_bis: z.string().optional(),
  ETr1_Jahr1_Menge: z.string().optional(),
  ETr1_Jahr1_Leerstand: z.string().optional(),
  ETr1_Jahr1_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr1_Jahr1_Leerstand_von: z.string().optional(),
  ETr1_Jahr1_Leerstand_bis: z.string().optional(),

  // Jahr 2 für ETr1
  ETr1_Jahr2_von: z.string().optional(),
  ETr1_Jahr2_bis: z.string().optional(),
  ETr1_Jahr2_Menge: z.string().optional(),
  ETr1_Jahr2_Leerstand: z.string().optional(),
  ETr1_Jahr2_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr1_Jahr2_Leerstand_von: z.string().optional(),
  ETr1_Jahr2_Leerstand_bis: z.string().optional(),

  // Jahr 3 für ETr1
  ETr1_Jahr3_von: z.string().optional(),
  ETr1_Jahr3_bis: z.string().optional(),
  ETr1_Jahr3_Menge: z.string().optional(),
  ETr1_Jahr3_Leerstand: z.string().optional(),
  ETr1_Jahr3_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr1_Jahr3_Leerstand_von: z.string().optional(),
  ETr1_Jahr3_Leerstand_bis: z.string().optional(),

  // Energieträger 2 (ETr2)
  ETr2_Kategorie: z.string().optional(),
  ETr2_Heizung: z.enum(['0', '1']).default('0'),
  ETr2_TWW: z.enum(['0', '1']).default('0'),
  ETr2_ZusatzHz: z.enum(['0', '1']).default('0'),
  ETr2_Lueften: z.enum(['0', '1']).default('0'),
  ETr2_Licht: z.enum(['0', '1']).default('0'),
  ETr2_Kuehlen: z.enum(['0', '1']).default('0'),
  ETr2_Sonst: z.enum(['0', '1']).default('0'),
  // Primärenergiefaktor is fixed at 1 per business requirements - hidden from user interface
  ETr2_PrimFaktor: z.string().default('1'),
  ETr2_Anteil_erneuerbar: z.string().optional(),
  ETr2_Anteil_KWK: z.string().optional(),
  ETr2_isFw: z.enum(['0', '1']).default('0'),
  ETr2_gebaeudeNahErzeugt: z.enum(['0', '1']).default('0'),
  ETr2_Name: z.string().optional(),

  // Jahr 1 für ETr2
  ETr2_Jahr1_von: z.string().optional(),
  ETr2_Jahr1_bis: z.string().optional(),
  ETr2_Jahr1_Menge: z.string().optional(),
  ETr2_Jahr1_Leerstand: z.string().optional(),
  ETr2_Jahr1_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr2_Jahr1_Leerstand_von: z.string().optional(),
  ETr2_Jahr1_Leerstand_bis: z.string().optional(),

  // Jahr 2 für ETr2
  ETr2_Jahr2_von: z.string().optional(),
  ETr2_Jahr2_bis: z.string().optional(),
  ETr2_Jahr2_Menge: z.string().optional(),
  ETr2_Jahr2_Leerstand: z.string().optional(),
  ETr2_Jahr2_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr2_Jahr2_Leerstand_von: z.string().optional(),
  ETr2_Jahr2_Leerstand_bis: z.string().optional(),

  // Jahr 3 für ETr2
  ETr2_Jahr3_von: z.string().optional(),
  ETr2_Jahr3_bis: z.string().optional(),
  ETr2_Jahr3_Menge: z.string().optional(),
  ETr2_Jahr3_Leerstand: z.string().optional(),
  ETr2_Jahr3_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr2_Jahr3_Leerstand_von: z.string().optional(),
  ETr2_Jahr3_Leerstand_bis: z.string().optional(),

  // Energieträger 3 (ETr3)
  ETr3_Kategorie: z.string().optional(),
  ETr3_Heizung: z.enum(['0', '1']).default('0'),
  ETr3_TWW: z.enum(['0', '1']).default('0'),
  ETr3_ZusatzHz: z.enum(['0', '1']).default('0'),
  ETr3_Lueften: z.enum(['0', '1']).default('0'),
  ETr3_Licht: z.enum(['0', '1']).default('0'),
  ETr3_Kuehlen: z.enum(['0', '1']).default('0'),
  ETr3_Sonst: z.enum(['0', '1']).default('0'),
  // Primärenergiefaktor is fixed at 1 per business requirements - hidden from user interface
  ETr3_PrimFaktor: z.string().default('1'),
  ETr3_Anteil_erneuerbar: z.string().optional(),
  ETr3_Anteil_KWK: z.string().optional(),
  ETr3_isFw: z.enum(['0', '1']).default('0'),
  ETr3_gebaeudeNahErzeugt: z.enum(['0', '1']).default('0'),
  ETr3_Name: z.string().optional(),

  // Jahr 1 für ETr3
  ETr3_Jahr1_von: z.string().optional(),
  ETr3_Jahr1_bis: z.string().optional(),
  ETr3_Jahr1_Menge: z.string().optional(),
  ETr3_Jahr1_Leerstand: z.string().optional(),
  ETr3_Jahr1_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr3_Jahr1_Leerstand_von: z.string().optional(),
  ETr3_Jahr1_Leerstand_bis: z.string().optional(),

  // Jahr 2 für ETr3
  ETr3_Jahr2_von: z.string().optional(),
  ETr3_Jahr2_bis: z.string().optional(),
  ETr3_Jahr2_Menge: z.string().optional(),
  ETr3_Jahr2_Leerstand: z.string().optional(),
  ETr3_Jahr2_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr3_Jahr2_Leerstand_von: z.string().optional(),
  ETr3_Jahr2_Leerstand_bis: z.string().optional(),

  // Jahr 3 für ETr3
  ETr3_Jahr3_von: z.string().optional(),
  ETr3_Jahr3_bis: z.string().optional(),
  ETr3_Jahr3_Menge: z.string().optional(),
  ETr3_Jahr3_Leerstand: z.string().optional(),
  ETr3_Jahr3_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr3_Jahr3_Leerstand_von: z.string().optional(),
  ETr3_Jahr3_Leerstand_bis: z.string().optional(),

  // Verbrauchsrechnungen (Datei-Uploads) - Support for multiple files per year
  verbrauchsrechnung1: z.array(z.string()).optional(),
  verbrauchsrechnung2: z.array(z.string()).optional(),
  verbrauchsrechnung3: z.array(z.string()).optional(),
});

type VerbrauchFormValues = z.infer<typeof verbrauchSchema>;

export const VerbrauchPage = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [certificateType, setCertificateType] = useState<CertificateType>('WG/V');
  const { activeCertificateId } = useCertificate();
  const [uploadedFiles, setUploadedFiles] = useState<{
    verbrauchsrechnung1?: File[];
    verbrauchsrechnung2?: File[];
    verbrauchsrechnung3?: File[];
  }>({});
  const [uploadProgress, setUploadProgress] = useState<{
    verbrauchsrechnung1?: { [fileName: string]: number };
    verbrauchsrechnung2?: { [fileName: string]: number };
    verbrauchsrechnung3?: { [fileName: string]: number };
  }>({});
  const [uploadErrors, setUploadErrors] = useState<{
    verbrauchsrechnung1?: string[];
    verbrauchsrechnung2?: string[];
    verbrauchsrechnung3?: string[];
  }>({});

  const [initialValues, setInitialValues] = useState<Partial<VerbrauchFormValues>>({
    ETr1_Kategorie: 'BK_GAS',
    ETr1_Heizung: '1',
    ETr1_TWW: '1',
    ETr1_ZusatzHz: '0',
    ETr1_Lueften: '0',
    ETr1_Licht: '0',
    ETr1_Kuehlen: '0',
    ETr1_Sonst: '0',
    ETr1_PrimFaktor: '1',
    ETr1_Anteil_erneuerbar: '',
    ETr1_Anteil_KWK: '',
    ETr1_isFw: '0',
    ETr1_gebaeudeNahErzeugt: '0',
    ETr1_Name: '',
    ETr1_Jahr1_von: '',
    ETr1_Jahr1_bis: '',
    ETr1_Jahr1_Menge: '',
    ETr1_Jahr1_Leerstand: '0',
    ETr1_Jahr1_Leerstand_hasLeerstand: '0',
    ETr1_Jahr1_Leerstand_von: '',
    ETr1_Jahr1_Leerstand_bis: '',
    ETr1_Jahr2_von: '',
    ETr1_Jahr2_bis: '',
    ETr1_Jahr2_Menge: '',
    ETr1_Jahr2_Leerstand: '0',
    ETr1_Jahr2_Leerstand_hasLeerstand: '0',
    ETr1_Jahr2_Leerstand_von: '',
    ETr1_Jahr2_Leerstand_bis: '',
    ETr1_Jahr3_von: '',
    ETr1_Jahr3_bis: '',
    ETr1_Jahr3_Menge: '',
    ETr1_Jahr3_Leerstand: '0',
    ETr1_Jahr3_Leerstand_hasLeerstand: '0',
    ETr1_Jahr3_Leerstand_von: '',
    ETr1_Jahr3_Leerstand_bis: '',

    ETr2_Kategorie: '',
    ETr2_Heizung: '0',
    ETr2_TWW: '0',
    ETr2_ZusatzHz: '0',
    ETr2_Lueften: '0',
    ETr2_Licht: '0',
    ETr2_Kuehlen: '0',
    ETr2_Sonst: '0',
    ETr2_PrimFaktor: '1',
    ETr2_Anteil_erneuerbar: '',
    ETr2_Anteil_KWK: '',
    ETr2_isFw: '0',
    ETr2_gebaeudeNahErzeugt: '0',
    ETr2_Name: '',

    ETr3_Kategorie: '',
    ETr3_Heizung: '0',
    ETr3_TWW: '0',
    ETr3_ZusatzHz: '0',
    ETr3_Lueften: '0',
    ETr3_Licht: '0',
    ETr3_Kuehlen: '0',
    ETr3_Sonst: '0',
    ETr3_PrimFaktor: '1',
    ETr3_Anteil_erneuerbar: '',
    ETr3_Anteil_KWK: '',
    ETr3_isFw: '0',
    ETr3_gebaeudeNahErzeugt: '0',
    ETr3_Name: '',

    // File upload fields - default to empty arrays
    verbrauchsrechnung1: [],
    verbrauchsrechnung2: [],
    verbrauchsrechnung3: [],
  });

  const [isLoading, setIsLoading] = useState(true);
  const [showEtr2, setShowEtr2] = useState(false);
  const [showEtr3, setShowEtr3] = useState(false);

  // Fetch certificate type
  const { data: certificateData } = useQuery({
    queryKey: ['energieausweise', 'certificate_type', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('certificate_type')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Update certificate type when data is fetched
  useEffect(() => {
    if (certificateData?.certificate_type) {
      setCertificateType(certificateData.certificate_type as CertificateType);
    }
  }, [certificateData]);

  // Fetch existing data
  const { data: existingData, isError: dataError, error: fetchError } = useQuery({
    queryKey: ['energieausweise', 'verbrauch', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('verbrauchsdaten')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Update form values when data is fetched
  useEffect(() => {
    if (existingData && existingData.verbrauchsdaten) {
      setInitialValues(prev => ({
        ...prev,
        ...(existingData.verbrauchsdaten as Partial<VerbrauchFormValues>)
      }));

      // Show ETr2 and ETr3 sections if they have data
      const verbrauchsdaten = existingData.verbrauchsdaten as Partial<VerbrauchFormValues>;
      if (verbrauchsdaten.ETr2_Kategorie) {
        setShowEtr2(true);
      }
      if (verbrauchsdaten.ETr3_Kategorie) {
        setShowEtr3(true);
      }
    }
    setIsLoading(false);
  }, [existingData]);

  // File validation constants
  const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB per file
  const MAX_TOTAL_SIZE_PER_YEAR = 20 * 1024 * 1024; // 20MB per year
  const MAX_TOTAL_SIZE_ALL = 50 * 1024 * 1024; // 50MB total
  const MAX_FILES_PER_YEAR = 5;
  const ALLOWED_FILE_TYPES = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  // Validate files before upload
  const validateFiles = (files: FileList, fieldName: 'verbrauchsrechnung1' | 'verbrauchsrechnung2' | 'verbrauchsrechnung3') => {
    const errors: string[] = [];
    const currentFiles = uploadedFiles[fieldName] || [];

    // Check if adding these files would exceed the limit per year
    if (currentFiles.length + files.length > MAX_FILES_PER_YEAR) {
      errors.push(`Maximal ${MAX_FILES_PER_YEAR} Dateien pro Jahr erlaubt`);
    }

    // Check individual file sizes and types
    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      if (file.size > MAX_FILE_SIZE) {
        errors.push(`${file.name}: Datei zu groß (max. 5MB)`);
      }

      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        errors.push(`${file.name}: Dateityp nicht unterstützt (nur PDF, JPG, PNG, WebP)`);
      }
    }

    // Check total size per year
    const currentYearSize = currentFiles.reduce((sum, file) => sum + file.size, 0);
    const newFilesSize = Array.from(files).reduce((sum, file) => sum + file.size, 0);
    if (currentYearSize + newFilesSize > MAX_TOTAL_SIZE_PER_YEAR) {
      errors.push('Gesamtgröße pro Jahr überschreitet 20MB');
    }

    // Check total size across all years
    const allFiles = Object.values(uploadedFiles).flat().filter(Boolean);
    const totalCurrentSize = allFiles.reduce((sum, file) => sum + file.size, 0);
    if (totalCurrentSize + newFilesSize > MAX_TOTAL_SIZE_ALL) {
      errors.push('Gesamtgröße aller Dateien überschreitet 50MB');
    }

    return errors;
  };

  // Handle file uploads with multiple file support
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>, fieldName: 'verbrauchsrechnung1' | 'verbrauchsrechnung2' | 'verbrauchsrechnung3') => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    // Validate files
    const validationErrors = validateFiles(files, fieldName);
    if (validationErrors.length > 0) {
      setUploadErrors(prev => ({ ...prev, [fieldName]: validationErrors }));
      return;
    }

    // Clear previous errors
    setUploadErrors(prev => ({ ...prev, [fieldName]: [] }));

    // Add files to uploaded files state
    const newFiles = Array.from(files);
    setUploadedFiles(prev => ({
      ...prev,
      [fieldName]: [...(prev[fieldName] || []), ...newFiles]
    }));

    // Upload each file
    const uploadPromises = newFiles.map(file => uploadSingleFile(file, fieldName));

    try {
      const uploadedUrls = await Promise.all(uploadPromises);

      // Update form field with all file URLs
      const currentUrls = form.getFieldValue(fieldName) || [];
      const allUrls = [...currentUrls, ...uploadedUrls];
      form.setFieldValue(fieldName, allUrls);

    } catch (error) {
      console.error('Fehler beim Hochladen:', error);
      setSubmitError(`Fehler beim Hochladen der Dateien: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`);
    }
  };

  // Upload a single file to Supabase Storage
  const uploadSingleFile = async (file: File, fieldName: 'verbrauchsrechnung1' | 'verbrauchsrechnung2' | 'verbrauchsrechnung3'): Promise<string> => {
    if (!activeCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

    // Get user ID for the path structure
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('Nicht eingeloggt');

    const userId = user.user.id;
    const filePath = `${userId}/${activeCertificateId}/${fieldName}_${file.name}`;

    // Create a custom upload with progress tracking
    const xhr = new XMLHttpRequest();

    // Set up progress tracking
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        const percent = Math.round((event.loaded / event.total) * 100);
        setUploadProgress(prev => ({
          ...prev,
          [fieldName]: {
            ...(prev[fieldName] || {}),
            [file.name]: percent
          }
        }));
      }
    });

    // Create a promise to handle the upload
    const uploadPromise = new Promise<{ data: any; error: any }>((resolve) => {
      xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE) {
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve({ data: true, error: null });
          } else {
            resolve({ data: null, error: new Error(`Upload failed with status ${xhr.status}`) });
          }
        }
      };
    });

    // Get the presigned URL for upload
    const { data: uploadData } = await supabase.storage
      .from('certificateuploads')
      .createSignedUploadUrl(filePath);

    if (!uploadData) {
      throw new Error('Fehler beim Erstellen der Upload-URL');
    }

    // Configure the request
    xhr.open('PUT', uploadData.signedUrl);

    // Send the file
    xhr.send(file);

    // Wait for the upload to complete
    const { error } = await uploadPromise;

    if (error) throw error;

    // Store just the file path in the database (not the full URL)
    // We'll generate signed URLs on demand when the user wants to view the file
    return filePath;
  };

  // Define the mutation for saving data to Supabase
  const saveMutation = useMutation({
    mutationFn: async (data: VerbrauchFormValues) => {
      if (!activeCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

      const { data: result, error } = await supabase
        .from('energieausweise')
        .update({
          verbrauchsdaten: data,
          updated_at: new Date().toISOString(),
        })
        .eq('id', activeCertificateId)
        .select();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['energieausweise', activeCertificateId] });
      queryClient.invalidateQueries({ queryKey: ['energieausweise', 'verbrauch', activeCertificateId] });
      // Navigate to the next page (Zusammenfassung)
      navigate({ to: '/erfassen/zusammenfassung' });
    },
    onError: (error) => {
      setSubmitError(`Fehler beim Speichern: ${error.message}`);
    },
  });

  // Create the form
  const form = useForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      setSubmitError(null);

      // Filter out ETr2 and ETr3 data if they're not visible
      const filteredValue = { ...value };

      // If ETr2 is not shown, remove all ETr2 fields
      if (!showEtr2) {
        Object.keys(filteredValue).forEach(key => {
          if (key.startsWith('ETr2_')) {
            // Use type assertion to tell TypeScript this is a valid key
            delete (filteredValue as Record<string, any>)[key];
          }
        });
      }

      // If ETr3 is not shown, remove all ETr3 fields
      if (!showEtr3) {
        Object.keys(filteredValue).forEach(key => {
          if (key.startsWith('ETr3_')) {
            // Use type assertion to tell TypeScript this is a valid key
            delete (filteredValue as Record<string, any>)[key];
          }
        });
      }

      // If certificate type is not NWG/V, remove the conditional fields
      if (certificateType !== 'NWG/V') {
        // Remove fields that should only be shown for NWG/V
        ['ETr1_ZusatzHz', 'ETr1_Lueften', 'ETr1_Licht', 'ETr1_Kuehlen', 'ETr1_Sonst'].forEach(key => {
          delete (filteredValue as Record<string, any>)[key];
        });

        // Also remove these fields from ETr2 and ETr3 if they exist
        if (showEtr2) {
          ['ETr2_ZusatzHz', 'ETr2_Lueften', 'ETr2_Licht', 'ETr2_Kuehlen', 'ETr2_Sonst'].forEach(key => {
            delete (filteredValue as Record<string, any>)[key];
          });
        }

        if (showEtr3) {
          ['ETr3_ZusatzHz', 'ETr3_Lueften', 'ETr3_Licht', 'ETr3_Kuehlen', 'ETr3_Sonst'].forEach(key => {
            delete (filteredValue as Record<string, any>)[key];
          });
        }
      }

      saveMutation.mutate(filteredValue as VerbrauchFormValues);
    },
  });

  // Function to calculate and update all related dates for a given energy carrier
  const updateCalculatedDates = (startDate: string, prefix: 'ETr1' | 'ETr2' | 'ETr3') => {
    if (!startDate) return;

    const calculatedDates = calculateAllDatesFromStartDate(startDate);

    // Update all calculated dates
    form.setFieldValue(`${prefix}_Jahr1_bis`, calculatedDates.jahr1Bis);
    form.setFieldValue(`${prefix}_Jahr2_von`, calculatedDates.jahr2Von);
    form.setFieldValue(`${prefix}_Jahr2_bis`, calculatedDates.jahr2Bis);
    form.setFieldValue(`${prefix}_Jahr3_von`, calculatedDates.jahr3Von);
    form.setFieldValue(`${prefix}_Jahr3_bis`, calculatedDates.jahr3Bis);
  };

  // Helper component for form fields
  const FormField = ({
    name,
    label,
    type = 'text',
    placeholder = '',
    required = false,
    onChangeCallback
  }: {
    name: keyof VerbrauchFormValues;
    label: string;
    type?: string;
    placeholder?: string;
    required?: boolean;
    onChangeCallback?: (value: string) => void;
  }) => {
    const { state, handleChange, handleBlur } = useField({
      name,
      form,
    });

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      handleChange(value);
      // Call the callback immediately for real-time updates
      if (onChangeCallback) {
        onChangeCallback(value);
      }
    };

    return (
      <div className="mb-4">
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <input
          id={name}
          name={name}
          type={type}
          value={state.value ?? ''}
          onChange={handleInputChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
          }`}
        />
        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
        )}
      </div>
    );
  };

  // Helper component for select fields
  const SelectField = ({
    name,
    label,
    options,
    required = false
  }: {
    name: keyof VerbrauchFormValues;
    label: string;
    options: { value: string; label: string }[];
    required?: boolean;
  }) => {
    const { state, handleChange, handleBlur } = useField({
      name,
      form,
    });

    return (
      <div className="mb-4">
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <select
          id={name}
          name={name}
          value={state.value ?? ''}
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
          }`}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
        )}
      </div>
    );
  };



  // Helper function to remove a file
  const removeFile = (fieldName: 'verbrauchsrechnung1' | 'verbrauchsrechnung2' | 'verbrauchsrechnung3', fileIndex: number) => {
    // Remove from uploaded files
    setUploadedFiles(prev => {
      const currentFiles = prev[fieldName] || [];
      const newFiles = currentFiles.filter((_, index) => index !== fileIndex);
      return { ...prev, [fieldName]: newFiles };
    });

    // Remove from form values
    const currentUrls = form.getFieldValue(fieldName) || [];
    const newUrls = currentUrls.filter((_: string, index: number) => index !== fileIndex);
    form.setFieldValue(fieldName, newUrls);

    // Clear any progress for removed file
    setUploadProgress(prev => {
      const currentProgress = prev[fieldName] || {};
      const fileName = (uploadedFiles[fieldName] || [])[fileIndex]?.name;
      if (fileName && currentProgress[fileName]) {
        const { [fileName]: removed, ...rest } = currentProgress;
        return { ...prev, [fieldName]: rest };
      }
      return prev;
    });
  };

  // Helper component for enhanced file upload fields with multiple file support
  const FileUploadField = ({
    name,
    label,
    required = false
  }: {
    name: 'verbrauchsrechnung1' | 'verbrauchsrechnung2' | 'verbrauchsrechnung3';
    label: string;
    required?: boolean;
  }) => {
    const { state } = useField({
      name,
      form,
    });

    const currentFiles = uploadedFiles[name] || [];
    const currentProgress = uploadProgress[name] || {};
    const currentErrors = uploadErrors[name] || [];
    const uploadedUrls = (state.value as string[]) || [];

    // Calculate total size for this year
    const totalSize = currentFiles.reduce((sum, file) => sum + file.size, 0);
    const totalSizeMB = (totalSize / (1024 * 1024)).toFixed(1);

    return (
      <div className="mb-6 p-4 border border-gray-200 rounded-lg">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          {label} {required && <span className="text-red-500">*</span>}
        </label>


        {/* File Upload Button */}
        <div className="mb-4">
          <input
            type="file"
            id={name}
            accept=".pdf,.jpg,.jpeg,.png,.webp"
            multiple
            onChange={(e) => handleFileChange(e, name)}
            className="hidden"
            disabled={currentFiles.length >= MAX_FILES_PER_YEAR}
          />
          <label
            htmlFor={name}
            className={`inline-flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors cursor-pointer ${
              currentFiles.length >= MAX_FILES_PER_YEAR
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-green-600 text-white hover:bg-green-700'
            }`}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            {currentFiles.length === 0 ? 'Dateien auswählen' : 'Weitere Dateien hinzufügen'}
          </label>

          {currentFiles.length >= MAX_FILES_PER_YEAR && (
            <p className="text-xs text-gray-500 mt-1">
              Maximum von {MAX_FILES_PER_YEAR} Dateien erreicht
            </p>
          )}
        </div>

        {/* File Size Info */}
        {currentFiles.length > 0 && (
          <div className="mb-4 text-xs text-gray-600">
            <span>Gesamtgröße: {totalSizeMB} MB / 20 MB</span>
            <span className="ml-4">Dateien: {currentFiles.length} / {MAX_FILES_PER_YEAR}</span>
          </div>
        )}

        {/* Error Messages */}
        {currentErrors.length > 0 && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <h4 className="text-sm font-medium text-red-800 mb-1">Fehler beim Upload:</h4>
            <ul className="text-xs text-red-700 space-y-1">
              {currentErrors.map((error, index) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Uploaded Files List */}
        {currentFiles.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700">Hochgeladene Dateien:</h4>
            {currentFiles.map((file, index) => {
              const progress = currentProgress[file.name] || 0;
              const isUploading = progress > 0 && progress < 100;

              return (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded border">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <div className="flex-shrink-0">
                        {file.type === 'application/pdf' ? (
                          <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                          </svg>
                        ) : (
                          <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">{file.name}</p>
                        <p className="text-xs text-gray-500">{(file.size / 1024).toFixed(1)} KB</p>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    {isUploading && (
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 rounded-full h-1.5">
                          <div
                            className="bg-green-600 h-1.5 rounded-full transition-all duration-300"
                            style={{ width: `${progress}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">Wird hochgeladen... {progress}%</p>
                      </div>
                    )}
                  </div>

                  {/* Remove Button */}
                  <button
                    type="button"
                    onClick={() => removeFile(name, index)}
                    className="ml-2 p-1 text-red-500 hover:text-red-700 transition-colors"
                    disabled={isUploading}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              );
            })}
          </div>
        )}

        {/* Previously Uploaded Files (from database) */}
        {uploadedUrls.length > 0 && currentFiles.length === 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Bereits hochgeladene Dateien:</h4>
            <div className="space-y-2">
              {uploadedUrls.map((url, index) => (
                <FileWithSignedUrl
                  key={index}
                  path={url}
                  label={`Datei ${index + 1} anzeigen`}
                />
              ))}
            </div>
          </div>
        )}

        {/* Form Validation Errors */}
        {state.meta.errors.length > 0 && (
          <div className="mt-2">
            <p className="text-sm text-red-500">{state.meta.errors.join(', ')}</p>
          </div>
        )}
      </div>
    );
  };

  // Helper component for Leerstand field with conditional date-based calculation
  const LeerstandField = ({
    prefix,
    jahr,
    required = false
  }: {
    prefix: 'ETr1' | 'ETr2' | 'ETr3';
    jahr: 'Jahr1' | 'Jahr2' | 'Jahr3';
    required?: boolean;
  }) => {
    const hasLeerstandFieldName = `${prefix}_${jahr}_Leerstand_hasLeerstand` as keyof VerbrauchFormValues;
    const leerstandVonFieldName = `${prefix}_${jahr}_Leerstand_von` as keyof VerbrauchFormValues;
    const leerstandBisFieldName = `${prefix}_${jahr}_Leerstand_bis` as keyof VerbrauchFormValues;
    const leerstandFieldName = `${prefix}_${jahr}_Leerstand` as keyof VerbrauchFormValues;
    const periodVonFieldName = `${prefix}_${jahr}_von` as keyof VerbrauchFormValues;
    const periodBisFieldName = `${prefix}_${jahr}_bis` as keyof VerbrauchFormValues;

    const { state: hasLeerstandState, handleChange: handleHasLeerstandChange } = useField({
      name: hasLeerstandFieldName,
      form,
    });

    const { state: leerstandVonState, handleChange: handleLeerstandVonChange } = useField({
      name: leerstandVonFieldName,
      form,
    });

    const { state: leerstandBisState, handleChange: handleLeerstandBisChange } = useField({
      name: leerstandBisFieldName,
      form,
    });

    const { state: periodVonState } = useField({
      name: periodVonFieldName,
      form,
    });

    const { state: periodBisState } = useField({
      name: periodBisFieldName,
      form,
    });

    const hasLeerstand = hasLeerstandState.value === '1';

    // Calculate percentage when dates change
    const updateLeerstandPercentage = () => {
      if (hasLeerstand && leerstandVonState.value && leerstandBisState.value &&
          periodVonState.value && periodBisState.value) {
        const percentage = calculateLeerstandPercentage(
          leerstandVonState.value as string,
          leerstandBisState.value as string,
          periodVonState.value as string,
          periodBisState.value as string
        );
        form.setFieldValue(leerstandFieldName, percentage.toString());
      } else if (!hasLeerstand) {
        form.setFieldValue(leerstandFieldName, '0');
      }
    };

    // Update percentage when relevant fields change
    useEffect(() => {
      updateLeerstandPercentage();
    }, [hasLeerstand, leerstandVonState.value, leerstandBisState.value, periodVonState.value, periodBisState.value]);

    // Set default dates when hasLeerstand is enabled and period start date is available
    useEffect(() => {
      if (hasLeerstand && periodVonState.value && !leerstandVonState.value) {
        form.setFieldValue(leerstandVonFieldName, periodVonState.value);
        form.setFieldValue(leerstandBisFieldName, periodVonState.value);
      }
    }, [hasLeerstand, periodVonState.value]);

    const calculatedPercentage = hasLeerstand && leerstandVonState.value && leerstandBisState.value &&
                                 periodVonState.value && periodBisState.value
      ? calculateLeerstandPercentage(
          leerstandVonState.value as string,
          leerstandBisState.value as string,
          periodVonState.value as string,
          periodBisState.value as string
        )
      : 0;

    return (
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          War das Gebäude im relevanten Zeitraum leer? {required && <span className="text-red-500">*</span>}
        </label>

        <div className="space-y-3">
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                name={hasLeerstandFieldName}
                value="0"
                checked={hasLeerstandState.value === '0'}
                onChange={(e) => handleHasLeerstandChange(e.target.value)}
                className="mr-2"
              />
              Nein
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name={hasLeerstandFieldName}
                value="1"
                checked={hasLeerstandState.value === '1'}
                onChange={(e) => handleHasLeerstandChange(e.target.value)}
                className="mr-2"
              />
              Ja
            </label>
          </div>

          {hasLeerstand && (
            <div className="pl-4 border-l-2 border-gray-200 space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">
                    Leerstand von
                  </label>
                  <input
                    type="date"
                    value={leerstandVonState.value ?? ''}
                    onChange={(e) => handleLeerstandVonChange(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">
                    Leerstand bis
                  </label>
                  <input
                    type="date"
                    value={leerstandBisState.value ?? ''}
                    onChange={(e) => handleLeerstandBisChange(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500"
                  />
                </div>
              </div>

              {calculatedPercentage > 0 && (
                <div className="bg-green-50 p-3 rounded-md">
                  <p className="text-sm text-green-800">
                    <strong>Berechneter Leerstand:</strong> {calculatedPercentage.toFixed(2)}%
                  </p>
                  <p className="text-xs text-green-600 mt-1">
                    Dieser Wert wird automatisch basierend auf den eingegebenen Daten berechnet.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  // Energy carrier options
  const energietraegerOptions = [
    { value: '', label: 'Bitte wählen' },
    { value: 'BK_GAS', label: 'Erdgas' },
    { value: 'BK_OEL', label: 'Heizöl' },
    { value: 'BK_STROM', label: 'Strom' },
    { value: 'BK_PELLET', label: 'Holzpellets' },
    { value: 'BK_HACKSCHNITZEL', label: 'Holzhackschnitzel' },
    { value: 'BK_STUECKHOLZ', label: 'Stückholz' },
    { value: 'BK_KOHLE', label: 'Kohle' },
    { value: 'BK_BIOGAS', label: 'Biogas' },
    { value: 'BK_BIOOEL', label: 'Bioöl' },
    { value: 'BK_FW70', label: 'Fernwärme (70%)' },
    { value: 'BK_FW50', label: 'Fernwärme (50%)' },
    { value: 'BK_FW0', label: 'Fernwärme (0%)' },
    { value: 'BK_UMWELT', label: 'Umweltwärme' },
  ];



  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Energieverbrauch erfassen
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Bitte geben Sie die Verbrauchsdaten für bis zu drei Energieträger und drei Jahre ein.
        Sie können auch Ihre Verbrauchsrechnungen als Bild oder PDF hochladen.
      </p>

      <ActiveCertificateIndicator />

      {isLoading ? (
        <div className="bg-white shadow-md rounded-lg p-6 flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-2"></div>
            <p className="text-gray-600">Daten werden geladen...</p>
          </div>
        </div>
      ) : (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="bg-white shadow-md rounded-lg p-6"
        >
          {/* Energieträger 1 */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Energieträger 1
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <SelectField
                name="ETr1_Kategorie"
                label="Energieträger"
                options={energietraegerOptions}
                required
              />

              <FormField
                name="ETr1_Name"
                label="Bezeichnung"
                placeholder="z.B. Hauptheizung Gas"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <RadioField
                name="ETr1_Heizung"
                label="Für Heizung verwendet"
                form={form}
              />

              <RadioField
                name="ETr1_TWW"
                label="Für Trinkwarmwasser verwendet"
                form={form}
              />

              {certificateType === 'NWG/V' && (
                <RadioField
                  name="ETr1_ZusatzHz"
                  label="Als Zusatzheizung verwendet"
                  form={form}
                />
              )}
            </div>

            {certificateType === 'NWG/V' && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <RadioField
                  name="ETr1_Lueften"
                  label="Für Lüftung verwendet"
                  form={form}
                />

                <RadioField
                  name="ETr1_Licht"
                  label="Für Beleuchtung verwendet"
                  form={form}
                />

                <RadioField
                  name="ETr1_Kuehlen"
                  label="Für Kühlung verwendet"
                  form={form}
                />
              </div>
            )}

            {certificateType === 'NWG/V' && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <RadioField
                  name="ETr1_Sonst"
                  label="Für sonstige Zwecke verwendet"
                  form={form}
                />
              </div>
            )}

            {/* Primärenergiefaktor is fixed at 1 per business requirements - hidden from user interface */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <FormField
                name="ETr1_Anteil_erneuerbar"
                label="Anteil erneuerbar (%)"
                placeholder="z.B. 20"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <FormField
                name="ETr1_Anteil_KWK"
                label="KWK-Anteil (%)"
                placeholder="z.B. 30"
              />

              <RadioField
                name="ETr1_isFw"
                label="Ist Fernwärme"
                form={form}
              />

              <RadioField
                name="ETr1_gebaeudeNahErzeugt"
                label="Gebäudenah erzeugt"
                form={form}
              />
            </div>

            {/* Jahr 1 für ETr1 */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 1</h3>
              <p className="text-sm text-green-600 mb-3">
                💡 Tipp: Wenn Sie das Startdatum eingeben, werden alle anderen Zeiträume automatisch berechnet. Sie können diese anschließend bei Bedarf anpassen.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormField
                  name="ETr1_Jahr1_von"
                  label="Zeitraum von"
                  type="date"
                  onChangeCallback={(value) => updateCalculatedDates(value, 'ETr1')}
                />

                <FormField
                  name="ETr1_Jahr1_bis"
                  label="Zeitraum bis"
                  type="date"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormField
                  name="ETr1_Jahr1_Menge"
                  label="Verbrauchsmenge gesamt"
                  placeholder="z.B. 15000"
                />
              </div>

              <LeerstandField
                prefix="ETr1"
                jahr="Jahr1"
              />
            </div>

            {/* Jahr 2 für ETr1 */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 2</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormField
                  name="ETr1_Jahr2_von"
                  label="Zeitraum von"
                  type="date"
                />

                <FormField
                  name="ETr1_Jahr2_bis"
                  label="Zeitraum bis"
                  type="date"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormField
                  name="ETr1_Jahr2_Menge"
                  label="Verbrauchsmenge gesamt"
                  placeholder="z.B. 15000"
                />
              </div>

              <LeerstandField
                prefix="ETr1"
                jahr="Jahr2"
              />
            </div>

            {/* Jahr 3 für ETr1 */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 3</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormField
                  name="ETr1_Jahr3_von"
                  label="Zeitraum von"
                  type="date"
                />

                <FormField
                  name="ETr1_Jahr3_bis"
                  label="Zeitraum bis"
                  type="date"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormField
                  name="ETr1_Jahr3_Menge"
                  label="Verbrauchsmenge gesamt"
                  placeholder="z.B. 15000"
                />
              </div>

              <LeerstandField
                prefix="ETr1"
                jahr="Jahr3"
              />
            </div>
          </div>

          {/* Button to add Energieträger 2 */}
          {!showEtr2 && (
            <div className="mb-8">
              <button
                type="button"
                onClick={() => setShowEtr2(true)}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                + Weiteren Energieträger hinzufügen
              </button>
            </div>
          )}

          {/* Energieträger 2 */}
          {showEtr2 && (
            <div className="mb-8">
              <div className="flex justify-between items-center mb-4 pb-2 border-b">
                <h2 className="text-xl font-semibold text-gray-800">
                  Energieträger 2
                </h2>
                <button
                  type="button"
                  onClick={() => {
                    // Clear all ETr2 fields when removing
                    Object.keys(form.state.values).forEach(key => {
                      if (key.startsWith('ETr2_')) {
                        form.setFieldValue(key as keyof VerbrauchFormValues, '');
                      }
                    });
                    setShowEtr2(false);
                  }}
                  className="text-red-600 hover:text-red-800"
                >
                  Entfernen
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <SelectField
                  name="ETr2_Kategorie"
                  label="Energieträger"
                  options={energietraegerOptions}
                />

                <FormField
                  name="ETr2_Name"
                  label="Bezeichnung"
                  placeholder="z.B. Zusatzheizung Strom"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <RadioField
                  name="ETr2_Heizung"
                  label="Für Heizung verwendet"
                  form={form}
                />

                <RadioField
                  name="ETr2_TWW"
                  label="Für Trinkwarmwasser verwendet"
                  form={form}
                />

                {certificateType === 'NWG/V' && (
                  <RadioField
                    name="ETr2_ZusatzHz"
                    label="Als Zusatzheizung verwendet"
                    form={form}
                  />
                )}
              </div>

              {certificateType === 'NWG/V' && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <RadioField
                    name="ETr2_Lueften"
                    label="Für Lüftung verwendet"
                    form={form}
                  />

                  <RadioField
                    name="ETr2_Licht"
                    label="Für Beleuchtung verwendet"
                    form={form}
                  />

                  <RadioField
                    name="ETr2_Kuehlen"
                    label="Für Kühlung verwendet"
                    form={form}
                  />
                </div>
              )}

              {certificateType === 'NWG/V' && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <RadioField
                    name="ETr2_Sonst"
                    label="Für sonstige Zwecke verwendet"
                    form={form}
                  />
                </div>
              )}

              {/* Primärenergiefaktor is fixed at 1 per business requirements - hidden from user interface */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <FormField
                  name="ETr2_Anteil_erneuerbar"
                  label="Anteil erneuerbar (%)"
                  placeholder="z.B. 20"
                />

                <FormField
                  name="ETr2_Anteil_KWK"
                  label="KWK-Anteil (%)"
                  placeholder="z.B. 30"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <RadioField
                  name="ETr2_isFw"
                  label="Ist Fernwärme"
                  form={form}
                />

                <RadioField
                  name="ETr2_gebaeudeNahErzeugt"
                  label="Gebäudenah erzeugt"
                  form={form}
                />
              </div>

              {/* Jahr 1 für ETr2 */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 1</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr2_Jahr1_von"
                    label="Zeitraum von"
                    type="date"
                    onChangeCallback={(value) => updateCalculatedDates(value, 'ETr2')}
                  />

                  <FormField
                    name="ETr2_Jahr1_bis"
                    label="Zeitraum bis"
                    type="date"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr2_Jahr1_Menge"
                    label="Verbrauchsmenge gesamt"
                    placeholder="z.B. 15000"
                  />
                </div>

                <LeerstandField
                  prefix="ETr2"
                  jahr="Jahr1"
                />
              </div>

              {/* Jahr 2 für ETr2 */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 2</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr2_Jahr2_von"
                    label="Zeitraum von"
                    type="date"
                  />

                  <FormField
                    name="ETr2_Jahr2_bis"
                    label="Zeitraum bis"
                    type="date"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr2_Jahr2_Menge"
                    label="Verbrauchsmenge gesamt"
                    placeholder="z.B. 15000"
                  />
                </div>

                <LeerstandField
                  prefix="ETr2"
                  jahr="Jahr2"
                />
              </div>

              {/* Jahr 3 für ETr2 */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 3</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr2_Jahr3_von"
                    label="Zeitraum von"
                    type="date"
                  />

                  <FormField
                    name="ETr2_Jahr3_bis"
                    label="Zeitraum bis"
                    type="date"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr2_Jahr3_Menge"
                    label="Verbrauchsmenge gesamt"
                    placeholder="z.B. 15000"
                  />
                </div>

                <LeerstandField
                  prefix="ETr2"
                  jahr="Jahr3"
                />
              </div>
            </div>
          )}

          {/* Button to add Energieträger 3 */}
          {showEtr2 && !showEtr3 && (
            <div className="mb-8">
              <button
                type="button"
                onClick={() => setShowEtr3(true)}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                + Weiteren Energieträger hinzufügen
              </button>
            </div>
          )}

          {/* Energieträger 3 */}
          {showEtr3 && (
            <div className="mb-8">
              <div className="flex justify-between items-center mb-4 pb-2 border-b">
                <h2 className="text-xl font-semibold text-gray-800">
                  Energieträger 3
                </h2>
                <button
                  type="button"
                  onClick={() => {
                    // Clear all ETr3 fields when removing
                    Object.keys(form.state.values).forEach(key => {
                      if (key.startsWith('ETr3_')) {
                        form.setFieldValue(key as keyof VerbrauchFormValues, '');
                      }
                    });
                    setShowEtr3(false);
                  }}
                  className="text-red-600 hover:text-red-800"
                >
                  Entfernen
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <SelectField
                  name="ETr3_Kategorie"
                  label="Energieträger"
                  options={energietraegerOptions}
                />

                <FormField
                  name="ETr3_Name"
                  label="Bezeichnung"
                  placeholder="z.B. Solaranlage"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <RadioField
                  name="ETr3_Heizung"
                  label="Für Heizung verwendet"
                  form={form}
                />

                <RadioField
                  name="ETr3_TWW"
                  label="Für Trinkwarmwasser verwendet"
                  form={form}
                />

                {certificateType === 'NWG/V' && (
                  <RadioField
                    name="ETr3_ZusatzHz"
                    label="Als Zusatzheizung verwendet"
                    form={form}
                  />
                )}
              </div>

              {certificateType === 'NWG/V' && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <RadioField
                    name="ETr3_Lueften"
                    label="Für Lüftung verwendet"
                    form={form}
                  />

                  <RadioField
                    name="ETr3_Licht"
                    label="Für Beleuchtung verwendet"
                    form={form}
                  />

                  <RadioField
                    name="ETr3_Kuehlen"
                    label="Für Kühlung verwendet"
                    form={form}
                  />
                </div>
              )}

              {certificateType === 'NWG/V' && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <RadioField
                    name="ETr3_Sonst"
                    label="Für sonstige Zwecke verwendet"
                    form={form}
                  />
                </div>
              )}

              {/* Primärenergiefaktor is fixed at 1 per business requirements - hidden from user interface */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <FormField
                  name="ETr3_Anteil_erneuerbar"
                  label="Anteil erneuerbar (%)"
                  placeholder="z.B. 20"
                />

                <FormField
                  name="ETr3_Anteil_KWK"
                  label="KWK-Anteil (%)"
                  placeholder="z.B. 30"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <RadioField
                  name="ETr3_isFw"
                  label="Ist Fernwärme"
                  form={form}
                />

                <RadioField
                  name="ETr3_gebaeudeNahErzeugt"
                  label="Gebäudenah erzeugt"
                  form={form}
                />
              </div>

              {/* Jahr 1 für ETr3 */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 1</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr3_Jahr1_von"
                    label="Zeitraum von"
                    type="date"
                    onChangeCallback={(value) => updateCalculatedDates(value, 'ETr3')}
                  />

                  <FormField
                    name="ETr3_Jahr1_bis"
                    label="Zeitraum bis"
                    type="date"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr3_Jahr1_Menge"
                    label="Verbrauchsmenge gesamt"
                    placeholder="z.B. 15000"
                  />
                </div>

                <LeerstandField
                  prefix="ETr3"
                  jahr="Jahr1"
                />
              </div>

              {/* Jahr 2 für ETr3 */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 2</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr3_Jahr2_von"
                    label="Zeitraum von"
                    type="date"
                  />

                  <FormField
                    name="ETr3_Jahr2_bis"
                    label="Zeitraum bis"
                    type="date"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr3_Jahr2_Menge"
                    label="Verbrauchsmenge gesamt"
                    placeholder="z.B. 15000"
                  />
                </div>

                <LeerstandField
                  prefix="ETr3"
                  jahr="Jahr2"
                />
              </div>

              {/* Jahr 3 für ETr3 */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 3</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr3_Jahr3_von"
                    label="Zeitraum von"
                    type="date"
                  />

                  <FormField
                    name="ETr3_Jahr3_bis"
                    label="Zeitraum bis"
                    type="date"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr3_Jahr3_Menge"
                    label="Verbrauchsmenge gesamt"
                    placeholder="z.B. 15000"
                  />
                </div>

                <LeerstandField
                  prefix="ETr3"
                  jahr="Jahr3"
                />
              </div>
            </div>
          )}

          {/* Verbrauchsrechnungen Upload */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Verbrauchsrechnungen hochladen
            </h2>
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="text-lg font-medium text-blue-800 mb-2">Flexible Upload-Optionen</h3>
              <p className="text-blue-700 mb-3">
                Laden Sie Ihre Verbrauchsrechnungen der letzten drei Jahre in einem der folgenden Formate hoch:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
                <div>
                  <h4 className="font-medium mb-2">📄 PDF-Option:</h4>
                  <ul className="space-y-1 text-xs">
                    <li>• Eine PDF-Datei pro Jahr</li>
                    <li>• Alle Rechnungen in einem Dokument</li>
                    <li>• Maximale Dateigröße: 5MB</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">🖼️ Bild-Option:</h4>
                  <ul className="space-y-1 text-xs">
                    <li>• Bis zu 5 Bilder pro Jahr</li>
                    <li>• Formate: JPG, PNG, WebP</li>
                    <li>• Max. 5MB pro Bild, 20MB pro Jahr</li>
                  </ul>
                </div>
              </div>
              <p className="text-xs text-blue-600 mt-3">
                <strong>Gesamtlimit:</strong> Maximal 50MB für alle Uploads zusammen
              </p>
            </div>

            <div className="space-y-4">
              <FileUploadField
                name="verbrauchsrechnung1"
                label="Verbrauchsrechnung Jahr 1"
              />

              <FileUploadField
                name="verbrauchsrechnung2"
                label="Verbrauchsrechnung Jahr 2"
              />

              <FileUploadField
                name="verbrauchsrechnung3"
                label="Verbrauchsrechnung Jahr 3"
              />
            </div>
          </div>

          {submitError && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {submitError}
            </div>
          )}

          <div className="flex justify-between mt-8">
            <Link
              to={certificateType === 'WG/B' ? '/erfassen/tww-lueftung' : '/erfassen/gebaeudedetails2'}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
            >
              Zurück
            </Link>
            <button
              type="submit"
              disabled={form.state.isSubmitting || saveMutation.isPending}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-300"
            >
              {form.state.isSubmitting || saveMutation.isPending ? 'Wird gespeichert...' : 'Weiter zur Zusammenfassung'}
            </button>
          </div>
        </form>
      )}

      {dataError && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <p>Fehler beim Laden der Daten: {fetchError instanceof Error ? fetchError.message : 'Unbekannter Fehler'}</p>
          <p className="mt-2">Bitte versuchen Sie es später erneut oder kontaktieren Sie den Support.</p>
        </div>
      )}
    </div>
  );
};